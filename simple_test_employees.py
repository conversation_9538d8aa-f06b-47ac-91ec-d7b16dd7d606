#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لجدول الموظفين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class SimpleEmployeesTable(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # إنشاء جدول بسيط
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        
        # عناوين مطابقة للعملاء والموردين
        headers = [
            "🔢 ID",          # رقم مسلسل
            "👤 اسم العامل",   # الاسم كاملاً
            "📱 الهاتف",      # رقم الهاتف
            "📧 الإيميل",      # البريد الإلكتروني
            "📍 العنوان",     # العنوان
            "💰 الراتب",      # الراتب الشهري
            "📝 ملاحظات",     # الملاحظات
            "🔄 الحالة",      # حالة العامل
            "📅 التاريخ"      # تاريخ التوظيف
        ]
        
        self.table.setHorizontalHeaderLabels(headers)
        
        # إضافة بيانات تجريبية
        sample_data = [
            ["1", "أحمد محمد علي", "+966 50 123 4567", "<EMAIL>", "الرياض", "15,000", "خبرة 5 سنوات", "🟢 نشط", "2023-01-15"],
            ["2", "فاطمة أحمد السالم", "+966 55 987 6543", "<EMAIL>", "جدة", "12,500", "قائدة فريق", "🟢 نشط", "2022-08-20"],
            ["3", "محمد عبدالله الغامدي", "+966 56 456 7890", "<EMAIL>", "الدمام", "8,750", "دقيق في العمل", "🆕 جديد", "2024-01-10"]
        ]
        
        self.table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                item.setForeground(QColor("#000000"))
                self.table.setItem(row, col, item)
        
        # تطبيق أعراض الأعمدة
        widths = [120, 150, 220, 180, 150, 200, 105, 120, 154]
        for col, width in enumerate(widths):
            self.table.setColumnWidth(col, width)
        
        # تطبيق تصميم
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
                gridline-color: #dee2e6;
                border: 3px solid #000000;
                border-radius: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: white;
                border: 2px solid #5a67d8;
                padding: 8px;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        self.table.setAlternatingRowColors(True)
        
        layout.addWidget(self.table)
        self.setLayout(layout)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار جدول الموظفين البسيط")
        self.setGeometry(100, 100, 1200, 600)
        
        # إنشاء الجدول
        self.employees_table = SimpleEmployeesTable()
        self.setCentralWidget(self.employees_table)

def main():
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
