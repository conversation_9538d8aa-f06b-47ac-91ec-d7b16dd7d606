#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جدول المصروفات المحدث
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class SimpleExpensesTable(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # إنشاء جدول بسيط
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        
        # عناوين مطابقة للعملاء والموردين
        headers = [
            "🔢 ID",          # رقم مسلسل
            "💸 العنوان",     # عنوان المصروف
            "💰 المبلغ",      # المبلغ
            "📅 التاريخ",      # تاريخ المصروف
            "🏷️ الفئة",       # فئة المصروف
            "🏪 المورد",      # المورد
            "📝 ملاحظات",     # الملاحظات
            "🔄 الحالة",      # حالة المصروف
            "📅 التاريخ"      # تاريخ الإنشاء
        ]
        
        self.table.setHorizontalHeaderLabels(headers)
        
        # إضافة بيانات تجريبية
        sample_data = [
            ["1", "شراء مواد بناء", "8,500", "2024-01-15", "مواد", "مورد البناء", "مواد أساسية", "💸 مصروف", "2024-01-15"],
            ["2", "فاتورة كهرباء", "1,200", "2024-01-10", "خدمات", "شركة الكهرباء", "فاتورة شهرية", "💸 مصروف", "2024-01-10"],
            ["3", "صيانة معدات", "3,000", "2024-01-05", "صيانة", "ورشة الصيانة", "صيانة دورية", "💸 مصروف", "2024-01-05"]
        ]
        
        self.table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                
                # تلوين خاص للمبلغ
                if col == 2:  # عمود المبلغ
                    amount = int(value.replace(",", ""))
                    if amount > 5000:
                        item.setForeground(QColor("#dc2626"))
                        item.setBackground(QColor("#fee2e2"))
                    elif amount > 1000:
                        item.setForeground(QColor("#f59e0b"))
                        item.setBackground(QColor("#fef3c7"))
                    else:
                        item.setForeground(QColor("#6b7280"))
                        item.setBackground(QColor("#f3f4f6"))
                # تلوين خاص للحالة
                elif col == 7:  # عمود الحالة
                    item.setForeground(QColor("#dc2626"))
                    item.setBackground(QColor("#fee2e2"))
                else:
                    item.setForeground(QColor("#000000"))
                
                self.table.setItem(row, col, item)
        
        # تطبيق أعراض الأعمدة
        widths = [120, 200, 200, 154, 150, 150, 105, 120, 154]
        for col, width in enumerate(widths):
            self.table.setColumnWidth(col, width)
        
        # تطبيق تصميم
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
                gridline-color: #dee2e6;
                border: 3px solid #000000;
                border-radius: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                color: white;
                border: 2px solid #5a67d8;
                padding: 8px;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        self.table.setAlternatingRowColors(True)
        
        layout.addWidget(self.table)
        self.setLayout(layout)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار جدول المصروفات البسيط")
        self.setGeometry(100, 100, 1200, 600)
        
        # إنشاء الجدول
        self.expenses_table = SimpleExpensesTable()
        self.setCentralWidget(self.expenses_table)

def main():
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
