#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جدول الموظفين المحدث
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from database import init_db, get_session, User
from ui.employees import EmployeesWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار جدول الموظفين المحدث")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد قاعدة البيانات
        init_db()
        self.session = get_session()
        
        # إنشاء واجهة الموظفين
        self.employees_widget = EmployeesWidget(self.session)
        
        # إعداد النافذة
        central_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(self.employees_widget)
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

def main():
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
